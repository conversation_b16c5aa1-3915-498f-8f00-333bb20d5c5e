<template>
  <div class="agent-chat">
    <!-- 智能体信息栏 -->
    <div class="agent-info-bar" v-if="selectedAgent">
      <div class="agent-avatar">
        <img :src="selectedAgent.agentAvatar || selectedAgent.avatar || defaultAvatar"
          :alt="selectedAgent.agentName || selectedAgent.name" />
      </div>
      <div class="agent-details">
        <h3 class="agent-name">{{ selectedAgent.agentName || selectedAgent.name }}</h3>
        <p class="agent-description">{{ selectedAgent.agentDesc || selectedAgent.description }}</p>
      </div>
      <div class="agent-actions">
        <el-button size="small" @click="clearChat">清空对话</el-button>
      </div>
    </div>
    <div class="chat-container">
      <!-- 会话列表 -->
      <div class="chat-list" v-if="selectedAgent">
        <div class="session-header">
          <h4>会话历史</h4>
          <el-button size="mini" type="text" @click="loadSessionList">
            <i class="el-icon-refresh"></i>
          </el-button>
        </div>
        <div class="session-list" v-loading="sessionLoading">
          <div v-for="session in sessionList" :key="session.guid" class="session-item"
            :class="{ active: currentSessionGuid === session.guid }" @click="selectSession(session)">
            <div class="session-info">
              <div class="session-title">{{ session.sessionTitle }}</div>
              <div class="session-time">{{ session.displayTime }}</div>
              <div class="session-preview" v-if="session.lastMessage">
                {{ session.lastMessage.content }}
              </div>
            </div>
            <div class="session-actions">
              <el-dropdown @command="handleSessionAction" trigger="click">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{ action: 'delete', session }">删除会话</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'clear', session }">清空消息</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
      <div class="chat-content">
        <!-- 未选择智能体的提示 -->
        <div v-if="!selectedAgent" class="no-agent-selected">
          <div class="empty-state">
            <i class="el-icon-chat-dot-round"></i>
            <h3>请选择一个智能体开始对话</h3>
            <p>从智能体广场选择一个智能体，开始您的AI对话之旅</p>
            <el-button type="primary" @click="goToMarketplace">前往智能体广场</el-button>
          </div>
        </div>

        <!-- 对话区域 -->
        <div v-if="selectedAgent" class="chat-area">
          <!-- 智能体详情和预设问题 -->
          <div v-if="chatMessages.length === 0" class="agent-intro">
            <div class="agent-detail">
              <div class="agent-avatar-large">
                <img
                  :src="agentDetail.agentAvatar || selectedAgent.agentAvatar || selectedAgent.avatar || defaultAvatar"
                  :alt="agentDetail.agentName || selectedAgent.agentName || selectedAgent.name" />
              </div>
              <h3>{{ agentDetail.agentName || selectedAgent.agentName || selectedAgent.name }}</h3>
              <p class="agent-desc">{{ agentDetail.agentDesc || selectedAgent.agentDesc || selectedAgent.description }}
              </p>

              <!-- 预设问题 -->
              <div v-if="agentDetail.commonQuestions && agentDetail.commonQuestions.length > 0"
                class="common-questions">
                <h4>常见问题</h4>
                <div class="question-list">
                  <div v-for="(question, index) in agentDetail.commonQuestions" :key="index" class="question-item"
                    @click="sendPresetQuestion(question)">
                    {{ question }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 对话历史 -->
          <div class="chat-history" ref="chatHistory" v-loading="messagesLoading">
            <div v-for="(message, index) in chatMessages" :key="index" class="message-item">
              <div class="message" :class="message.role">
                <div v-if="message.role === 'assistant'" class="avatar">
                  <img
                    :src="agentDetail.agentAvatar || selectedAgent.agentAvatar || selectedAgent.avatar || defaultAvatar"
                    :alt="agentDetail.agentName || selectedAgent.agentName || selectedAgent.name" />
                </div>
                <div class="content">
                  <div class="message-content">
                    <div v-if="message.starting" class="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <div v-else v-html="formatMessage(message.content)"></div>
                  </div>
                  <div class="message-time">{{ formatTime(message.timestamp || message.createTime) }}</div>
                </div>
                <div v-if="message.role === 'user'" class="avatar">
                  <i class="el-icon-user-solid"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <div class="input-container">
              <el-input v-model="inputMessage" type="textarea" :rows="3" placeholder="请输入您的问题..."
                @keydown.ctrl.enter="sendMessage" :disabled="isLoading" />
              <div class="input-actions">
                <span class="shortcut-tip">Ctrl + Enter 发送</span>
                <el-button type="primary" @click="sendMessage" :loading="isLoading" :disabled="!inputMessage.trim()">
                  发送
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import {
  getMessageHistoryApi,
  getSessionMessagesApi,
  agentDetailApi,
  deleteSessionApi,
  deleteAllMessagesApi,
  saveMsgApi
} from '@/apis/agent'
import config from '@/apis/config'

export default {
  name: 'agent-chat',

  props: {
    selectedAgent: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      chatMessages: [],
      inputMessage: '',
      isLoading: false,
      conversationId: '',
      defaultAvatar: 'https://ai-v2.deepcity.cn/default/head.png',

      // 会话相关
      sessionList: [],
      sessionLoading: false,
      currentSessionGuid: '',
      messagesLoading: false,

      // 智能体详情
      agentDetail: {}
    }
  },

  watch: {
    selectedAgent: {
      handler(newAgent, oldAgent) {
        if (newAgent && (!oldAgent || newAgent.guid !== oldAgent.guid)) {
          this.initAgent()
        }
      },
      immediate: true
    }
  },

  methods: {
    async initAgent() {
      console.log(this.selectedAgent, '-----------this.selectedAgent')
      if (!this.selectedAgent) return

      // 重置状态
      this.chatMessages = []
      this.conversationId = ''
      this.currentSessionGuid = ''
      this.agentDetail = {}

      // 加载智能体详情
      await this.loadAgentDetail()

      // 加载会话列表
      await this.loadSessionList()

      // 如果有会话，选择第一个
      if (this.sessionList.length > 0) {
        await this.selectSession(this.sessionList[0])
      }
    },

    async loadAgentDetail() {
      try {
        const res = await agentDetailApi({
          merchantGuid: this.$store.state.merchantGuid,
          agentGuid: this.selectedAgent.guid || this.selectedAgent.id
        })

        if (res.code === 0) {
          this.agentDetail = res.data
        }
      } catch (error) {
        console.error('加载智能体详情失败:', error)
      }
    },

    async loadSessionList() {
      this.sessionLoading = true
      try {
        const res = await getMessageHistoryApi({
          merchantGuid: this.$store.state.merchantGuid,
          pageSize: 10,
          page: 1
        })

        if (res.code === 0) {
          this.sessionList = res.data.data || []
        }
      } catch (error) {
        console.error('加载会话列表失败:', error)
      } finally {
        this.sessionLoading = false
      }
    },

    async selectSession(session) {
      this.currentSessionGuid = session.guid
      await this.loadSessionMessages(session.guid)
    },

    async loadSessionMessages(sessionGuid) {
      this.messagesLoading = true
      this.chatMessages = []

      try {
        // 调用获取会话消息的API
        const res = await getSessionMessagesApi({
          merchantGuid: this.$store.state.merchantGuid,
          sessionGuid: sessionGuid
        })

        if (res.code === 0 && res.data && res.data.length > 0) {
          // 转换消息格式
          this.chatMessages = res.data.map(msg => ({
            role: msg.messageType === 1 ? 'user' : 'assistant',
            content: msg.content,
            timestamp: msg.createTime,
            messageType: msg.messageType
          }))
        } else {
          // 如果没有历史消息，添加欢迎消息
          if (this.agentDetail.welcomeMessage) {
            this.chatMessages.push({
              role: 'assistant',
              content: this.agentDetail.welcomeMessage,
              timestamp: new Date(),
              messageType: 2
            })
          }
        }

        this.scrollToBottom()
      } catch (error) {
        console.error('加载会话消息失败:', error)
        // 出错时也显示欢迎消息
        if (this.agentDetail.welcomeMessage) {
          this.chatMessages.push({
            role: 'assistant',
            content: this.agentDetail.welcomeMessage,
            timestamp: new Date(),
            messageType: 2
          })
        }
      } finally {
        this.messagesLoading = false
      }
    },

    sendPresetQuestion(question) {
      this.inputMessage = question
      this.sendMessage()
    },

    async handleSessionAction(command) {
      const { action, session } = command

      if (action === 'delete') {
        await this.deleteSession(session)
      } else if (action === 'clear') {
        await this.clearSessionMessages(session)
      }
    },

    async deleteSession(session) {
      try {
        await this.$confirm('确定要删除这个会话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await deleteSessionApi({
          merchantGuid: this.$store.state.merchantGuid,
          sessionGuid: session.guid
        })

        if (res.code === 0) {
          this.$message.success('会话已删除')
          await this.loadSessionList()

          // 如果删除的是当前会话，清空聊天记录
          if (this.currentSessionGuid === session.guid) {
            this.chatMessages = []
            this.currentSessionGuid = ''
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除会话失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    async clearSessionMessages(session) {
      try {
        await this.$confirm('确定要清空这个会话的所有消息吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await deleteAllMessagesApi({
          merchantGuid: this.$store.state.merchantGuid,
          sessionGuid: session.guid
        })

        if (res.code === 0) {
          this.$message.success('消息已清空')

          // 如果清空的是当前会话，重新加载消息
          if (this.currentSessionGuid === session.guid) {
            await this.loadSessionMessages(session.guid)
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('清空消息失败:', error)
          this.$message.error('清空失败')
        }
      }
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return

      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''

      // 添加用户消息
      this.chatMessages.push({
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      })

      // 添加AI回复占位符
      this.chatMessages.push({
        role: 'assistant',
        content: '',
        starting: true,
        timestamp: new Date()
      })

      this.scrollToBottom()
      this.isLoading = true

      try {
        // 使用智能体API保存消息
        const msgReq = {
          merchantGuid: this.$store.state.merchantGuid,
          agentGuid: this.selectedAgent.guid || this.selectedAgent.id,
          sessionGuid: this.currentSessionGuid,
          content: userMessage,
          messageType: 1 // 用户消息
        }

        const res = await saveMsgApi(msgReq)

        if (res.code === 0) {
          // 如果是新会话，更新会话GUID
          if (!this.currentSessionGuid && res.data.sessionGuid) {
            this.currentSessionGuid = res.data.sessionGuid
            await this.loadSessionList() // 重新加载会话列表
          }

          await this.waitForAIResponse(res.data.msgId || res.data.messageId)
        } else {
          this.handleError('发送消息失败')
        }
      } catch (error) {
        console.error('发送消息错误:', error)
        this.handleError('发送消息失败，请重试')
      }
    },

    async waitForAIResponse(msgId) {
      const lastMessage = this.chatMessages[this.chatMessages.length - 1]

      try {
        // 使用智能体专用的SSE接口
        const link = `${config.baseUrl}useragent/api.AiAgentChat/sendAgentOpen?msgId=${msgId}&agentGuid=${this.selectedAgent.guid || this.selectedAgent.id}&sessionGuid=${this.currentSessionGuid}&merchantGuid=${this.$store.state.merchantGuid}`

        const source = new EventSource(link)

        source.onmessage = (event) => {
          const data = event.data

          if (data === '[DONE]') {
            lastMessage.starting = false
            lastMessage.finished = true
            this.isLoading = false
            source.close()

            // 响应完成后，重新加载会话列表以更新最新消息
            this.loadSessionList()
          } else if (data) {
            lastMessage.starting = false
            lastMessage.content += data.replace(/\\n/g, '\n')
            this.scrollToBottom()
          }
        }

        source.onerror = () => {
          this.handleError('AI回复失败')
          source.close()
        }

        source.onopen = () => {
          console.log('AI连接已建立')
        }

      } catch (error) {
        this.handleError('AI回复失败')
      }
    },

    handleError(message) {
      const lastMessage = this.chatMessages[this.chatMessages.length - 1]
      if (lastMessage && lastMessage.role === 'assistant') {
        lastMessage.starting = false
        lastMessage.content = message
        lastMessage.error = true
      }
      this.isLoading = false
      this.$message.error(message)
    },

    async clearChat() {
      if (!this.currentSessionGuid) {
        this.chatMessages = []
        this.$message.success('对话已清空')
        return
      }

      try {
        await this.$confirm('确定要清空当前对话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await deleteAllMessagesApi({
          merchantGuid: this.$store.state.merchantGuid,
          sessionGuid: this.currentSessionGuid
        })

        if (res.code === 0) {
          await this.loadSessionMessages(this.currentSessionGuid)
          this.$message.success('对话已清空')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('清空对话失败:', error)
          this.$message.error('清空失败')
        }
      }
    },


    goToMarketplace() {
      this.$emit('go-to-marketplace')
    },

    formatMessage(content) {
      if (!content) return ''
      return content.replace(/\n/g, '<br>')
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const chatHistory = this.$refs.chatHistory
        if (chatHistory) {
          chatHistory.scrollTop = chatHistory.scrollHeight
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;

  .agent-info-bar {
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    gap: 16px;

    .agent-avatar {
      img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .agent-details {
      flex: 1;

      .agent-name {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .agent-description {
        margin: 0;
        font-size: 14px;
        color: #666;
        line-height: 1.4;
      }
    }

    .agent-actions {
      display: flex;
      gap: 8px;
    }
  }

  .chat-container {
    flex: 1;
    display: flex;
    height: calc(100vh - 140px);

    .chat-list {
      width: 280px;
      background: white;
      border-right: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;

      .session-header {
        padding: 16px 20px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
          margin: 0;
          font-size: 16px;
          color: #333;
        }
      }

      .session-list {
        flex: 1;
        overflow-y: auto;

        .session-item {
          padding: 12px 20px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background-color 0.2s;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          &:hover {
            background: #f8f9fa;
          }

          &.active {
            background: #e8f4fd;
            border-left: 3px solid #7848f1;
          }

          .session-info {
            flex: 1;
            min-width: 0;

            .session-title {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .session-time {
              font-size: 12px;
              color: #999;
              margin-bottom: 4px;
            }

            .session-preview {
              font-size: 12px;
              color: #666;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .session-actions {
            margin-left: 8px;

            .el-dropdown-link {
              color: #999;
              cursor: pointer;
              padding: 4px;

              &:hover {
                color: #7848f1;
              }
            }
          }
        }
      }
    }

    .chat-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .no-agent-selected {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .empty-state {
      text-align: center;

      i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #333;
      }

      p {
        margin: 0 0 24px 0;
        color: #666;
      }
    }
  }

  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;

    .agent-intro {
      padding: 40px 20px;
      text-align: center;
      background: white;

      .agent-detail {
        max-width: 600px;
        margin: 0 auto;

        .agent-avatar-large {
          margin-bottom: 20px;

          img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }

        h3 {
          margin: 0 0 12px 0;
          font-size: 24px;
          font-weight: 600;
          color: #333;
        }

        .agent-desc {
          margin: 0 0 30px 0;
          font-size: 16px;
          color: #666;
          line-height: 1.6;
        }

        .common-questions {
          h4 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: #333;
          }

          .question-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;

            .question-item {
              padding: 12px 16px;
              background: #f8f9fa;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.2s;
              text-align: left;
              font-size: 14px;
              color: #333;

              &:hover {
                background: #e8f4fd;
                border-color: #7848f1;
                color: #7848f1;
              }
            }
          }
        }
      }
    }

    .chat-history {
      flex: 1;
      padding: 20px;
      overflow-y: auto;

      .message-item {
        margin-bottom: 20px;

        .message {
          display: flex;
          gap: 12px;

          &.user {
            flex-direction: row-reverse;

            .content {
              text-align: right;

              .message-content {
                background: #7848f1;
                color: white;
              }
            }
          }

          .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
            }

            i {
              font-size: 20px;
              color: #666;
            }
          }

          .content {
            flex: 1;
            max-width: 70%;

            .message-content {
              background: white;
              padding: 12px 16px;
              border-radius: 12px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              word-wrap: break-word;
              line-height: 1.5;

              .typing-indicator {
                display: flex;
                gap: 4px;

                span {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background: #999;
                  animation: typing 1.4s infinite ease-in-out;

                  &:nth-child(1) {
                    animation-delay: -0.32s;
                  }

                  &:nth-child(2) {
                    animation-delay: -0.16s;
                  }
                }
              }
            }

            .message-time {
              font-size: 12px;
              color: #999;
              margin-top: 4px;
            }
          }
        }
      }
    }

    .chat-input {
      background: white;
      border-top: 1px solid #e8e8e8;
      padding: 20px;

      .input-container {
        .el-textarea {
          margin-bottom: 12px;
        }

        .input-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .shortcut-tip {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}
</style>
