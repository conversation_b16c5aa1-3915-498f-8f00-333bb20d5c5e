# 智能体聊天组件功能测试

## 实现的功能

### 1. 会话历史列表
- ✅ 显示会话历史列表
- ✅ 会话项显示标题、时间、最后一条消息预览
- ✅ 支持选择会话
- ✅ 当前会话高亮显示
- ✅ 会话操作菜单（删除会话、清空消息）

### 2. 智能体详情展示
- ✅ 在聊天内容顶部显示智能体头像
- ✅ 显示智能体名称和描述
- ✅ 显示预设问题列表
- ✅ 点击预设问题自动发送

### 3. 消息历史加载
- ✅ 选择会话时加载历史消息
- ✅ 消息格式转换（用户/助手角色）
- ✅ 显示消息时间
- ✅ 支持加载状态显示

### 4. API集成
- ✅ getMessageHistoryApi - 获取会话列表
- ✅ getSessionMessagesApi - 获取会话消息
- ✅ agentDetailApi - 获取智能体详情
- ✅ deleteSessionApi - 删除会话
- ✅ deleteAllMessagesApi - 清空会话消息
- ✅ saveMsgApi - 发送消息

## 使用的API接口

### 1. 获取会话历史列表
```javascript
getMessageHistoryApi({
  merchantGuid: "商户GUID",
  pageSize: 10,
  page: 1
})
```

### 2. 获取智能体详情
```javascript
agentDetailApi({
  merchantGuid: "商户GUID",
  agentGuid: "智能体GUID"
})
```

### 3. 删除会话
```javascript
deleteSessionApi({
  merchantGuid: "商户GUID",
  sessionGuid: "会话GUID"
})
```

### 4. 清空会话消息
```javascript
deleteAllMessagesApi({
  merchantGuid: "商户GUID", 
  sessionGuid: "会话GUID"
})
```

## 组件特性

### 响应式布局
- 左侧会话列表固定宽度280px
- 右侧聊天区域自适应
- 支持移动端适配

### 用户体验
- 加载状态提示
- 错误处理和用户反馈
- 平滑滚动到底部
- 预设问题快速发送

### 数据流
1. 选择智能体 → 加载智能体详情
2. 加载会话列表 → 自动选择第一个会话
3. 选择会话 → 加载历史消息
4. 发送消息 → 实时流式响应
5. 响应完成 → 更新会话列表

## 注意事项

1. 需要确保store中有merchantGuid
2. 智能体对象需要包含guid或id字段
3. API返回格式需要符合预期结构
4. SSE连接需要正确的URL格式
