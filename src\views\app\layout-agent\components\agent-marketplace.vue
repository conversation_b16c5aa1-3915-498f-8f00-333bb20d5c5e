<template>
  <div class="agent-marketplace">
    <!-- 标题和搜索区域 -->
    <div class="header-section">
      <!-- <div class="title-area">
        <h1 class="page-title">智能体广场</h1>
        <p class="page-subtitle">发现优质智能体，探索工作新体验</p>
      </div> -->
      <div class="search-controls">
        <div class="search-bar">
          <el-input v-model="searchKeyword" placeholder="搜索智能体名称或描述" prefix-icon="el-icon-search" @input="handleSearch"
            clearable size="medium" />
        </div>
        <div class="sort-controls">
          <!-- <el-select v-model="sortBy" placeholder="最新发布" size="medium" @change="handleSort">
            <el-option label="最新发布" value="newest"></el-option>
            <el-option label="最受欢迎" value="popular"></el-option>
            <el-option label="评分最高" value="rating"></el-option>
          </el-select> -->
          <el-button icon="el-icon-search" size="medium" @click="refreshData">搜索</el-button>
        </div>
      </div>
    </div>

    <!-- 智能体列表 -->
    <div class="agent-container">
      <div class="agent-nav">
        <div class="nav-header">
          <span class="nav-title">分类</span>
        </div>
        <div class="nav-list">
          <div class="nav-item" :class="{ active: currentCategoryIndex === index }"
            v-for="(category, index) in categoryList" :key="category.guid" @click="switchCategory(index)">
            <div class="category-name">{{ category.categoryName }}</div>
            <div class="category-count">{{ category.agentCount }}</div>
          </div>
        </div>
      </div>

      <div class="agent-list" v-loading="loading">
        <!-- 调试信息 -->
        <div v-if="$store.state.debug" class="debug-info"
          style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
          <p>当前分类索引: {{ currentCategoryIndex }}</p>
          <p>分类数据数组长度: {{ categoryAgentData.length }}</p>
          <p>当前智能体列表长度: {{ currentAgentList.length }}</p>
          <p>加载状态: {{ loading }}</p>
        </div>

        <div class="agent-grid">
          <div v-for="agent in currentAgentList" :key="agent.guid" class="agent-card">
            <!-- 智能体头像和状态 -->
            <div class="agent-avatar">
              <img :src="agent.agentAvatar || defaultAvatar" :alt="agent.agentName" />
              <!-- <div class="agent-type-badge">{{ agent.agentTypeText }}</div> -->
            </div>

            <!-- 智能体信息 -->
            <div class="agent-info">
              <h3 class="agent-name">{{ agent.agentName }}</h3>
              <p class="agent-description">{{ agent.agentDesc }}</p>

              <!-- 统计信息 -->
              <div class="agent-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ agent.stats.viewCountText }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-star-on"></i>
                  {{ agent.stats.subscriberCountText }}
                </span>
              </div>

              <!-- 创作者信息 -->
              <div class="creator-info">
                <img :src="agent.creator.avatar" :alt="agent.creator.nickname" class="creator-avatar" />
                <span class="creator-name">{{ agent.creator.nickname }}</span>
              </div>

              <!-- 价格信息 -->
              <div class="agent-price">
                <span v-if="!agent.isPaid" class="free">免费</span>
                <span v-else class="paid">{{ agent.priceText }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="agent-actions">
              <el-button v-if="!agent.isSubscribed" type="primary" size="small" @click.stop="subscribeAgent(agent)">
                立即体验
              </el-button>
              <el-button v-else type="success" size="small" @click.stop="selectAgent(agent)">
                已订阅
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="currentAgentList.length === 0 && !loading" class="empty-state">
          <i class="el-icon-box"></i>
          <p>暂无智能体</p>
          <span>该分类下还没有智能体，敬请期待</span>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="pagination-wrapper">
          <el-pagination @current-change="handlePageChange" :current-page="pagination.current_page"
            :page-size="pagination.per_page" :total="pagination.total" layout="prev, pager, next, jumper" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAgentListApi, getCategoryListApi, subscribeAgentApi } from '@/apis/agent'

export default {
  name: 'agent-marketplace',

  data() {
    return {
      loading: false,
      searchKeyword: '',
      defaultAvatar: 'https://ai-v2.deepcity.cn/default/head.png',

      // 分类相关
      categoryList: [],
      currentCategoryIndex: 0,
      categoryAgentData: [],

      // 分页相关
      pagination: {
        current_page: 1,
        per_page: 10,
        total: 0,
        last_page: 1
      },

      merchantGuid: this.$store.state.merchantGuid
    }
  },

  computed: {
    currentAgentList() {
      console.log('计算currentAgentList:', {
        currentCategoryIndex: this.currentCategoryIndex,
        categoryAgentDataLength: this.categoryAgentData.length,
        categoryAgentData: this.categoryAgentData
      })

      const currentData = this.categoryAgentData[this.currentCategoryIndex]
      console.log('当前分类数据:', currentData)

      if (!currentData || !currentData.data) {
        console.log('没有数据或数据格式不正确')
        return []
      }

      let result = [...currentData.data]
      console.log('原始数据长度:', result.length)

      // 搜索过滤
      if (this.searchKeyword) {
        result = result.filter(agent =>
          agent.agentName.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          agent.agentDesc.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
        console.log('搜索过滤后长度:', result.length)
      }

      console.log('最终返回数据:', result)
      return result
    }
  },

  async mounted() {
    await this.initData()
  },

  methods: {
    // 初始化数据
    async initData() {
      await this.getCategoryList()
      if (this.categoryList.length > 0) {
        await this.getAgentList(0)
      }
    },

    // 获取分类列表
    async getCategoryList() {
      try {
        this.loading = true
        const res = await getCategoryListApi({
          merchantGuid: this.merchantGuid
        })

        if (res && res.data) {
          this.categoryList = res.data
          // 初始化categoryAgentData数组，填充null值确保响应式
          this.categoryAgentData = new Array(this.categoryList.length).fill(null)

          console.log('分类列表加载成功:', {
            categoryCount: this.categoryList.length,
            categories: this.categoryList.map(c => c.categoryName)
          })
        }
      } catch (error) {
        console.error('获取分类列表失败:', error)
        this.$message.error('获取分类列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取智能体列表
    async getAgentList(categoryIndex = null, page = 1) {
      if (this.categoryList.length === 0) return

      try {
        this.loading = true
        const targetIndex = categoryIndex !== null ? categoryIndex : this.currentCategoryIndex
        const category = this.categoryList[targetIndex]

        if (!category) return

        const res = await getAgentListApi({
          merchantGuid: this.merchantGuid,
          categoryGuid: category.guid,
          pageSize: this.pagination.per_page,
          page: page
        })

        if (res && res.data) {
          // 确保数组有足够的长度并使用Vue.set确保响应式
          while (this.categoryAgentData.length <= targetIndex) {
            this.categoryAgentData.push(null)
          }

          // 使用Vue.set确保响应式更新
          this.$set(this.categoryAgentData, targetIndex, res.data)

          // 更新分页信息
          this.pagination = {
            current_page: res.data.current_page || 1,
            per_page: res.data.per_page || 10,
            total: res.data.total || 0,
            last_page: res.data.last_page || 1
          }

          console.log('数据加载成功:', {
            targetIndex,
            dataLength: res.data.data ? res.data.data.length : 0,
            categoryAgentData: this.categoryAgentData
          })
        }
      } catch (error) {
        console.error('获取智能体列表失败:', error)
        this.$message.error('获取智能体列表失败')
      } finally {
        this.loading = false
      }
    },

    // 切换分类
    async switchCategory(index) {
      if (index === this.currentCategoryIndex) return

      this.currentCategoryIndex = index
      this.pagination.current_page = 1

      // 如果该分类数据未加载，则加载
      if (!this.categoryAgentData[index]) {
        await this.getAgentList(index)
      }
    },

    // 搜索处理
    handleSearch() {
      // 搜索逻辑已在computed中处理，这里可以添加防抖等优化
    },

    // 排序处理
    handleSort() {
      // 排序逻辑已在computed中处理
    },

    // 刷新数据
    async refreshData() {
      this.categoryAgentData = []
      await this.initData()
    },

    // 分页处理
    async handlePageChange(page) {
      this.pagination.current_page = page
      await this.getAgentList(this.currentCategoryIndex, page)
    },

    // 订阅智能体
    async subscribeAgent(agent) {
      try {
        const res = await subscribeAgentApi({
          merchantGuid: this.merchantGuid,
          agentGuid: agent.guid
        })

        if (res && res.code === 0) {
          // 更新本地状态
          agent.isSubscribed = true
          this.$message.success('订阅成功')
          // this.$emit('agent-subscribed', agent)
        } else {
          this.$message.error(res.msg || '订阅失败')
        }
      } catch (error) {
        console.error('订阅智能体失败:', error)
        this.$message.error('订阅失败，请稍后重试')
      }
    },

    // 选择智能体
    selectAgent(agent) {
      this.$emit('select-agent', agent)
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-marketplace {
  height: 100%;
  display: flex;
  flex-direction: column;
  // background: #f8f9fa;

  .header-section {
    background: white;
    padding: 24px 0px;
    border-bottom: 1px solid #e8e8e8;

    // .title-area {
    //   margin-bottom: 24px;

    //   .page-title {
    //     font-size: 28px;
    //     font-weight: 600;
    //     color: #333;
    //     margin: 0 0 8px 0;
    //     text-align: center;
    //   }

    //   .page-subtitle {
    //     font-size: 16px;
    //     color: #666;
    //     margin: 0;
    //     text-align: center;
    //   }
    // }

    .search-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;

      .search-bar {
        flex: 1;
        max-width: 400px;

        .el-input {
          width: 100%;
        }
      }

      .sort-controls {
        display: flex;
        gap: 12px;
        align-items: center;

        .el-select {
          width: 120px;
        }
      }
    }
  }

  .agent-container {
    display: flex;
    // gap: 24px;
    flex: 1;
  }

  .agent-nav {
    width: 200px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e8e8e8;
    height: fit-content;
    padding: 20px;
    margin-top: 20px;

    .nav-header {
      padding-bottom: 16px;

      .nav-title {
        font-size: 14px;
        color: #333;
      }
    }

    .nav-list {
      .nav-item {
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 12px;
        margin-bottom: 6px;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f8f9fa;
        }

        &.active {
          background: #7848f1;
          color: white;

          .category-count {
            color: #7848f1;
          }
        }

        .category-name {
          font-size: 12px;
          color: #333;
        }

        .category-count {
          font-size: 12px;
          color: #999;
          background: #f0f0f0;
          padding: 2px;
          border-radius: 10px;
          min-width: 20px;
          text-align: center;
        }

        &.active .category-name {
          color: white;
        }
      }
    }
  }

  .agent-list {
    flex: 1;
    background: transparent;
    overflow-y: auto;
    padding: 20px 20px;


    .agent-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 24px;
    }

    .agent-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;

      &:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        // border-color: #7848f1;
      }

      .agent-avatar {
        position: relative;
        text-align: center;
        margin-bottom: 16px;

        img {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          object-fit: cover;
          border: 3px solid #f0f0f0;
        }

        .agent-type-badge {
          position: absolute;
          top: -8px;
          right: calc(50% - 50px);
          background: linear-gradient(135deg, #7848f1, #9c6bff);
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 500;
          white-space: nowrap;
        }
      }

      .agent-info {
        .agent-name {
          font-size: 18px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: #333;
          text-align: center;
        }

        .agent-description {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          margin: 0 0 16px 0;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-align: center;
          min-height: 44px;
        }

        .agent-stats {
          display: flex;
          justify-content: center;
          gap: 16px;
          margin-bottom: 16px;
          font-size: 13px;

          .stat-item {
            display: flex;
            align-items: center;
            color: #999;

            i {
              margin-right: 4px;
              font-size: 14px;
            }
          }
        }

        .creator-info {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-bottom: 16px;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 20px;

          .creator-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
          }

          .creator-name {
            font-size: 13px;
            color: #666;
            font-weight: 500;
          }
        }

        .agent-price {
          text-align: center;
          margin-bottom: 16px;

          .free {
            color: #52c41a;
            font-weight: 600;
            font-size: 16px;
          }

          .paid {
            color: #7848f1;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }

      .agent-actions {
        text-align: center;

        .el-button {
          width: 100%;
          height: 40px;
          border-radius: 20px;
          font-weight: 500;

          &.el-button--primary {
            background: linear-gradient(135deg, #7848f1, #9c6bff);
            border: none;

            &:hover {
              background: linear-gradient(135deg, #6a3de8, #8b5cf6);
            }
          }

          &.el-button--success {
            background: #52c41a;
            border-color: #52c41a;

            &:hover {
              background: #73d13d;
              border-color: #73d13d;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 80px 20px;
      color: #999;

      i {
        font-size: 64px;
        margin-bottom: 16px;
        display: block;
        color: #d9d9d9;
      }

      p {
        font-size: 18px;
        margin: 0 0 8px 0;
        color: #666;
        font-weight: 500;
      }

      span {
        font-size: 14px;
        color: #999;
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      padding: 24px 0;
      border-top: 1px solid #f0f0f0;
      margin-top: 24px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .agent-marketplace {
    .agent-container {
      // padding: 16px 24px;
    }

    .agent-nav {
      width: 160px;
    }

    .agent-list .agent-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;
    }
  }
}

@media (max-width: 768px) {
  .agent-marketplace {
    .header-section {
      padding: 16px 20px;

      .search-controls {
        flex-direction: column;
        gap: 12px;

        .search-bar {
          max-width: none;
        }
      }
    }

    .agent-container {
      flex-direction: column;
      // padding: 16px 20px;
    }

    .agent-nav {
      width: 100%;

      .nav-list {
        display: flex;
        overflow-x: auto;

        .nav-item {
          flex-shrink: 0;
          min-width: 120px;
          border-bottom: none;
          border-right: 1px solid #f5f5f5;

          &:last-child {
            border-right: none;
          }
        }
      }
    }

    .agent-list .agent-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}
</style>
