<template>
  <el-main class="layout-agent">
    <el-container>
      <el-header class="header">
        <div class="item" :class="{ active: activeTab === 'marketplace' }" @click="switchTab('marketplace')">
          智能体广场
        </div>
        <div class="item" :class="{ active: activeTab === 'chat' }" @click="switchTab('chat')">
          智能体对话
        </div>
        <div class="item" :class="{ active: activeTab === 'my-agents' }" @click="switchTab('my-agents')">
          我的智能体
        </div>
      </el-header>
      <el-main class="agent-container">
        <!-- 智能体广场 -->
        <agent-marketplace v-if="activeTab === 'marketplace'" @select-agent="handleSelectAgent"></agent-marketplace>

        <!-- 智能体对话 -->
        <agent-chat v-if="activeTab === 'chat'" :selected-agent="selectedAgent"
          @go-to-marketplace="switchTab('marketplace')"></agent-chat>

        <!-- 我的智能体 -->
        <my-agents v-if="activeTab === 'my-agents'" @edit-agent="handleEditAgent"
          @test-agent="handleTestAgent"></my-agents>
      </el-main>
    </el-container>
  </el-main>
</template>

<script>
import AgentMarketplace from './components/agent-marketplace.vue'
import AgentChat from './components/agent-chat.vue'
import MyAgents from './components/my-agents.vue'

export default {
  name: 'layout-agent',
  components: {
    AgentMarketplace,
    AgentChat,
    MyAgents
  },

  data() {
    return {
      activeTab: 'marketplace', // 默认显示智能体广场
      selectedAgent: null // 选中的智能体
    }
  },

  methods: {
    switchTab(tab) {
      this.activeTab = tab
    },

    handleSelectAgent(agent) {
      this.selectedAgent = agent
      this.activeTab = 'chat' // 选择智能体后跳转到对话页面
    },

    handleEditAgent(agent) {
      // 处理编辑智能体逻辑
      console.log('编辑智能体:', agent)
    },

    handleTestAgent(agent) {
      this.selectedAgent = agent
      this.activeTab = 'chat' // 测试智能体时跳转到对话页面
    }
  }
}
</script>

<style lang="scss" scoped>
.layout-agent {
  padding: 0 !important;
  height: 100%;

  .header {
    display: flex;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    padding: 0 20px;

    .item {
      padding: 16px 24px;
      cursor: pointer;
      font-size: 16px;
      color: #666;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;

      &:hover {
        color: #7848f1;
      }

      &.active {
        color: #7848f1;
        border-bottom-color: #7848f1;
        font-weight: 500;
      }
    }
  }

  .agent-container {
    flex: 1;
    height: calc(100vh - 120px);
    overflow: hidden;
  }
}
</style>