<template>
  <div class="test-agent-chat">
    <h2>智能体聊天功能测试</h2>
    
    <!-- 测试按钮 -->
    <div class="test-controls">
      <el-button @click="testWithMockAgent" type="primary">测试模拟智能体</el-button>
      <el-button @click="testWithRealAgent" type="success">测试真实智能体</el-button>
      <el-button @click="clearTest" type="warning">清空测试</el-button>
    </div>
    
    <!-- 智能体聊天组件 -->
    <div class="chat-wrapper">
      <agent-chat :selected-agent="testAgent" />
    </div>
  </div>
</template>

<script>
import AgentChat from './components/agent-chat.vue'

export default {
  name: 'test-agent-chat',
  components: {
    AgentChat
  },
  
  data() {
    return {
      testAgent: null
    }
  },
  
  methods: {
    testWithMockAgent() {
      // 模拟智能体数据
      this.testAgent = {
        guid: 'test-agent-001',
        agentName: '测试智能体',
        agentDesc: '这是一个用于测试的智能体，可以回答各种问题',
        agentAvatar: 'https://ai-v2.deepcity.cn/default/head.png',
        agentType: 2,
        welcomeMessage: '你好！我是测试智能体，有什么可以帮助你的吗？',
        commonQuestions: [
          '你能做什么？',
          '如何使用你的功能？',
          '你有什么特色？',
          '能帮我写代码吗？'
        ],
        isPaid: 0,
        price: 0,
        isSubscribed: true
      }
    },
    
    testWithRealAgent() {
      // 使用真实的智能体数据（需要从API获取）
      this.testAgent = {
        guid: 'f40e3d5517434d339c359b86d747b05a',
        agentName: '专业法律顾问',
        agentDesc: '资深法律顾问，提供专业法律咨询服务',
        agentAvatar: 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/c25a423cbe2a436faad820bcfe39428f.png',
        agentType: 3,
        welcomeMessage: '你好啊，有什么法律问题可以帮助你的吗？',
        commonQuestions: [
          '合同纠纷如何处理？',
          '劳动法相关问题',
          '知识产权保护',
          '公司法律事务'
        ],
        isPaid: 1,
        price: 3,
        isSubscribed: true
      }
    },
    
    clearTest() {
      this.testAgent = null
    }
  }
}
</script>

<style lang="scss" scoped>
.test-agent-chat {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  h2 {
    margin: 0 0 20px 0;
    color: #333;
  }
  
  .test-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }
  
  .chat-wrapper {
    flex: 1;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
