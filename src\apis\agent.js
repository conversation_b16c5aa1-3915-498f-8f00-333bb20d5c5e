import { service as request } from '@/libs/request'
//获取智能体分类列表
export function getCategoryListApi(data) {
  return request.post('useragent/api.AiAgentPublic/categoryList', data)
}

//获取会话列表
export function getMySessionListApi(data) {
  return request.post('useragent/api.AiAgentChat/mySessionList', data)
}
//通过分类获取智能体列表
export function getAgentListApi(data) {
  return request.post('useragent/api.AiAgentSquare/agentList', data)
}


//订阅智能体
export function subscribeAgentApi(data) {
  return request.post('useragent/api.AiAgentSquare/subscribeAgent', data)
}

//修改会话标题
export function updateSessionTitleApi(data) {
  return request.post('useragent/api.AiAgentChat/updateSessionTitle', data)
}

//修改用户信息
export function updateUserInfoApi(data) {
  return request.post('user/api.userinfo/update', data)
}
//获取用户信息
export function getUserInfoApi(data) {
  return request.post('user/api.userinfo/index', data)
}
//获取我的智能体列表
export function getMyAgentListApi(data) {
  return request.post('useragent/api.AiAgent/myList', data)
}
//创建智能体
export function createAgentApi(data) {
  return request.post('useragent/api.AiAgent/create', data)
}
//Ai生成头像
export function generateAvatarApi(data) {
  return request.post('useragent/api.AiAgent/generateAvatar', data)
}
//获取我的智能体详情
export function getMyDetailApi(data) {
  return request.post('useragent/api.AiAgent/myDetail', data)
}
//修改我的智能体
export function updateMyAgentApi(data) {
  return request.post('useragent/api.AiAgent/update', data)
}
//获取会话历史列表
export function getMessageHistoryApi(data) {
  return request.post('useragent/api.AiAgentChat/messageHistory', data)
}

//获取会话消息详情
export function getSessionMessagesApi(data) {
  return request.post('useragent/api.AiAgentChat/sessionMessages', data)
}
export function saveMsgApi(data) {
  return request.post('useragent/api.AiAgentChat/saveMsg', data)
}

//获取规则信息
export function platformRulesApi(data) {
  return request.post('useragent/api.AiAgentPublic/platformRules', data)
}

//获取智能体详情
export function agentDetailApi(data) {
  return request.post('useragent/api.AiAgent/agentDetail', data)
}

//删除会话所有消息记录
export function deleteAllMessagesApi(data) {
  return request.post('useragent/api.AiAgentChat/deleteAllMessages', data)
}

//删除会话
export function deleteSessionApi(data) {
  return request.post('useragent/api.AiAgentChat/deleteSession', data)
}

//删除单条消息记录
export function deleteMessageApi(data) {
  return request.post('useragent/api.AiAgentChat/deleteMessage', data)
}

//收藏消息
export function collectMessageApi(data) {
  return request.post('useragent/api.AiAgentChat/collectMessage', data)
}

//取消收藏消息
export function cancelCollectMessageApi(data) {
  return request.post('useragent/api.AiAgentChat/uncollectMessage', data)
}

//获取我的收藏列表
export function getMyCollectionListApi(data) {
  return request.post('useragent/api.AiAgentChat/myCollectionList', data)
}

//创建购买订单
export function createPurchaseOrderApi(data) {
  return request.post('useragent/api.AiAgentSquare/createPurchaseOrder', data)
}

//查询购买订单
export function queryPurchaseOrderApi(data) {
  return request.post('useragent/api.AiAgentSquare/queryPurchaseOrder', data)
}

//创建会员套餐订阅订单
export function createVipOrderApi(data) {
  return request.post('useragent/api.AiAgentMembership/purchasePackage', data)
}

//获取会员套餐列表
export function getVipPackageListApi(data) {
  return request.post('useragent/api.AiAgentMembership/packageList', data)
}

//获取用户会员信息
export function getUserVipInfoApi(data) {
  return request.post('useragent/api.AiAgentMembership/myMembership', data)
}

//查询会员订阅订单
export function queryVipOrderApi(data) {
  return request.post('useragent/api.AiAgentMembership/queryPayment', data)
}


//获取邀请码
export function getInvitationCodeApi(data) {
  return request.post('user/api.userinfo/getInvitationCode', data)
}

//绑定邀请码
export function bindInvitationApi(data) {
  return request.post('useragent/api.AiAgent/bindInvitation', data)
}

//生成小程序码
export function generateMiniCodeApi(data) {
  return request.post('useragent/api.AiAgent/generateMiniCode', data)
}

//获取订阅列表
export function getSubscriptionListApi(data) {
  return request.post('useragent/api.AiAgentCreatorSubscription/creatorList', data)
}

//订阅智能体创作者
export function subscribeCreatorApi(data) {
  return request.post('useragent/api.AiAgentCreatorSubscription/purchaseCreatorSubscription', data)
}

//查询订阅订单
export function querySubscriptionOrderApi(data) {
  return request.post('useragent/api.AiAgentCreatorSubscription/queryPayment', data)
}

//获取用户订阅列表
export function getMySubscriptionListApi(data) {
  return request.post('useragent/api.AiAgentCreatorSubscription/myCreatorSubscriptions', data)
}

//获取订阅规则信息
export function getSubscriptionRuleApi(data) {
  return request.post('useragent/api.AiAgentMembership/subscriptionRule', data)
}

//获取banner图
export function showBannerUrlsApi(data) {
  return request.post('useragent/api.AiAgentPublic/showBannerUrls', data)
}

//设置会话置顶
export function setSessionTopApi(data) {
  return request.post('useragent/api.AiAgentChat/setSessionTop', data)
}

//删除智能体
export function deleteAgentApi(data) {
  return request.post('useragent/api.AiAgent/delete', data)
}

//获取banner列表
export function getBannerListApi(data) {
  return request.post('merchant/api.index/bannerList', data)
}
//获取我的收益
export function getMyEarningsApi(data) {
  return request.post('useragent/api.AiAgentFinance/myEarnings', data)
}

// 聊天点数商品列表
export function getChatGoodsApi(data) {
  return request.post('square/api.chatGoods/index', data)
}

// 购买聊天点数
export function buyChatGoodsApi(data) {
  return request.post('square/api.chatGoods/buy', data)
}
